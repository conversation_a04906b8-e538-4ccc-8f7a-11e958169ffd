name: Docker Build

permissions:
    contents: read
  
on:
  push:
    branches:
      - '**'
  pull_request:
    branches:
      - 'main'
      - 'releases/**'
      - 'release/**'
      - 'develop'

jobs:
  verify:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build for verification
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: false
          load: false