services:
  oracle:
    container_name: oracle_free
    image: container-registry.oracle.com/database/free:********-arm64
    environment:
      - OR<PERSON>LE_SID=${ORACLE_SID}
      - ORACLE_PDB=${OR<PERSON>LE_PDB}
      - ORACLE_PWD=${OR<PERSON>LE_PWD}
      - OR<PERSON><PERSON>_CHARACTERSET=${OR<PERSON><PERSON>_CHARACTERSET}
    ports:
      - "1521:1521"
    volumes:
      - ./init:/opt/oracle/scripts/startup

volumes:
  oracle_data: